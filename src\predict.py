import pandas as pd
import numpy as np
import joblib
from src.preprocessor import extract_features
import os

def predict_risk(input_data):
    """
    Predict rockfall risk and hours to event.

    Parameters:
    - input_data: Dict with sensor values

    Returns:
    - risk_level: 'Low', 'Moderate', 'High'
    - hours_to_rockfall: float or None
    """
    # Load models
    models_dir = os.path.join(os.path.dirname(__file__), '..', 'models')
    svm_model = joblib.load(os.path.join(models_dir, 'svm_classifier.pkl'))
    rf_class_model = joblib.load(os.path.join(models_dir, 'rf_classifier.pkl'))
    rf_reg_model = joblib.load(os.path.join(models_dir, 'rf_regressor.pkl'))

    # Create a DataFrame from input
    df = pd.DataFrame([input_data])
    df['timestamp'] = pd.Timestamp.now()

    # Use only original sensor features for prediction
    feature_cols = ['accel_x', 'accel_y', 'accel_z', 'vibration_freq', 'vibration_amp', 'tilt_angle', 'rainfall', 'humidity', 'temperature']
    X = df[feature_cols]

    # Classification predictions
    svm_pred = svm_model.predict(X)[0]
    rf_pred = rf_class_model.predict(X)[0]

    # Ensemble: majority vote
    event_pred = 1 if (svm_pred + rf_pred) >= 1 else 0

    if event_pred == 0:
        risk_level = 'Low'
        hours_to_rockfall = None
    else:
        # Regression for hours
        hours_pred = rf_reg_model.predict(X)[0]
        if hours_pred <= 24:
            risk_level = 'High'
        else:
            risk_level = 'Moderate'
        hours_to_rockfall = hours_pred

    return risk_level, hours_to_rockfall

def simulate_scenario(scenario):
    """
    Simulate input data for low, moderate, high scenarios.

    Parameters:
    - scenario: 'low', 'moderate', 'high'

    Returns:
    - input_data: Dict
    """
    if scenario == 'low':
        return {
            'accel_x': 0.1,
            'accel_y': 0.1,
            'accel_z': 9.8,
            'vibration_freq': 10,
            'vibration_amp': 1,
            'tilt_angle': 1,
            'rainfall': 0.1,
            'humidity': 50,
            'temperature': 20
        }
    elif scenario == 'moderate':
        return {
            'accel_x': 0.5,
            'accel_y': 0.5,
            'accel_z': 9.9,
            'vibration_freq': 50,
            'vibration_amp': 5,
            'tilt_angle': 5,
            'rainfall': 2,
            'humidity': 70,
            'temperature': 25
        }
    elif scenario == 'high':
        return {
            'accel_x': 1.0,
            'accel_y': 1.0,
            'accel_z': 10.0,
            'vibration_freq': 90,
            'vibration_amp': 9,
            'tilt_angle': 9,
            'rainfall': 5,
            'humidity': 85,
            'temperature': 30
        }
    else:
        raise ValueError("Invalid scenario")
