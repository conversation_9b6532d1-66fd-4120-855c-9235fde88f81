import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier, RandomForestRegressor
from sklearn.metrics import classification_report, mean_absolute_error
import joblib
import os

def train_models():
    """
    Train classification and regression models for rockfall prediction.
    """
    # Load processed data
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    df = pd.read_csv(os.path.join(data_dir, 'processed_features.csv'), parse_dates=['timestamp'])

    # Features for classification (rockfall_event)
    feature_cols = ['accel_x', 'accel_y', 'accel_z', 'vibration_freq', 'vibration_amp', 'tilt_angle', 'rainfall', 'humidity', 'temperature']
    X = df[feature_cols]
    y_class = df['rockfall_event']

    # Split data
    X_train, X_test, y_class_train, y_class_test = train_test_split(X, y_class, test_size=0.2, random_state=42)

    # Train SVM for classification
    svm_model = SVC(probability=True, random_state=42)
    svm_model.fit(X_train, y_class_train)
    svm_pred = svm_model.predict(X_test)
    print("SVM Classification Report:")
    print(classification_report(y_class_test, svm_pred))

    # Train Random Forest for classification
    rf_class_model = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_class_model.fit(X_train, y_class_train)
    rf_class_pred = rf_class_model.predict(X_test)
    print("Random Forest Classification Report:")
    print(classification_report(y_class_test, rf_class_pred))

    # For regression, use only rows where rockfall_event == 1
    df_reg = df[df['rockfall_event'] == 1].copy()
    X_reg = df_reg[feature_cols]
    y_reg = df_reg['hours_to_rockfall']

    X_reg_train, X_reg_test, y_reg_train, y_reg_test = train_test_split(X_reg, y_reg, test_size=0.2, random_state=42)

    # Train Random Forest for regression
    rf_reg_model = RandomForestRegressor(n_estimators=100, random_state=42)
    rf_reg_model.fit(X_reg_train, y_reg_train)
    rf_reg_pred = rf_reg_model.predict(X_reg_test)
    print("Random Forest Regression MAE:", mean_absolute_error(y_reg_test, rf_reg_pred))

    # Save models
    models_dir = os.path.join(os.path.dirname(__file__), '..', 'models')
    os.makedirs(models_dir, exist_ok=True)
    joblib.dump(svm_model, os.path.join(models_dir, 'svm_classifier.pkl'))
    joblib.dump(rf_class_model, os.path.join(models_dir, 'rf_classifier.pkl'))
    joblib.dump(rf_reg_model, os.path.join(models_dir, 'rf_regressor.pkl'))

    print("Models trained and saved.")

if __name__ == "__main__":
    train_models()
