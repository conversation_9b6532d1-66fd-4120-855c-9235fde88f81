import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def generate_dummy_data(num_samples=10000, sampling_rate_minutes=1):
    """
    Generate dummy sensor data for rockfall prediction.

    Parameters:
    - num_samples: Number of data points
    - sampling_rate_minutes: Minutes between samples

    Returns:
    - DataFrame with sensor data and targets
    """
    np.random.seed(42)

    # Generate timestamps
    start_time = datetime.now()
    timestamps = [start_time + timedelta(minutes=i) for i in range(num_samples)]

    # Generate sensor data
    accel_x = np.random.normal(0, 0.5, num_samples)  # m/s²
    accel_y = np.random.normal(0, 0.5, num_samples)
    accel_z = np.random.normal(9.8, 0.2, num_samples)  # Gravity + noise

    vibration_freq = np.random.uniform(0, 100, num_samples)  # Hz
    vibration_amp = np.random.uniform(0, 10, num_samples)  # Arbitrary units

    tilt_angle = np.random.uniform(0, 10, num_samples)  # Degrees

    rainfall = np.random.exponential(0.1, num_samples)  # mm/hour

    humidity = np.random.uniform(30, 90, num_samples)  # %

    temperature = np.random.uniform(10, 30, num_samples)  # °C

    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': timestamps,
        'accel_x': accel_x,
        'accel_y': accel_y,
        'accel_z': accel_z,
        'vibration_freq': vibration_freq,
        'vibration_amp': vibration_amp,
        'tilt_angle': tilt_angle,
        'rainfall': rainfall,
        'humidity': humidity,
        'temperature': temperature
    })

    # Simulate rockfall events
    # Rockfall more likely with high vibration, tilt, rainfall
    risk_score = (vibration_amp / 10) + (tilt_angle / 10) + (rainfall / 5) + np.random.normal(0, 0.5, num_samples)
    rockfall_event = (risk_score > 1.5).astype(int)

    # Hours to rockfall: if event, random hours 1-48, else NaN
    hours_to_rockfall = np.where(rockfall_event == 1, np.random.uniform(1, 48, num_samples), np.nan)

    df['rockfall_event'] = rockfall_event
    df['hours_to_rockfall'] = hours_to_rockfall

    return df

if __name__ == "__main__":
    df = generate_dummy_data()
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    os.makedirs(data_dir, exist_ok=True)
    df.to_csv(os.path.join(data_dir, 'sensor_data.csv'), index=False)
    print("Dummy data generated and saved to data/sensor_data.csv")
