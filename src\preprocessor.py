import pandas as pd
import numpy as np
from scipy.fft import fft
from sklearn.preprocessing import StandardScaler
import os

def extract_features(df, window_size=5):
    """
    Extract statistical and frequency domain features from sensor data.

    Parameters:
    - df: DataFrame with raw sensor data
    - window_size: rolling window size in minutes

    Returns:
    - DataFrame with features and target columns
    """
    df = df.copy()
    df.set_index('timestamp', inplace=True)

    # Resample to 1-minute intervals (if not already)
    df = df.resample('1T').mean().interpolate()

    # Statistical features for accelerometer
    for axis in ['accel_x', 'accel_y', 'accel_z']:
        df[f'{axis}_mean'] = df[axis].rolling(window=window_size).mean()
        df[f'{axis}_std'] = df[axis].rolling(window=window_size).std()
        df[f'{axis}_max'] = df[axis].rolling(window=window_size).max()
        df[f'{axis}_min'] = df[axis].rolling(window=window_size).min()

    # Rolling averages for vibration amplitude and frequency
    for col in ['vibration_freq', 'vibration_amp']:
        df[f'{col}_mean'] = df[col].rolling(window=window_size).mean()
        df[f'{col}_std'] = df[col].rolling(window=window_size).std()

    # Skip FFT features for now to speed up processing
    # df['vibration_fft_mean'] = 0
    # df['vibration_fft_max'] = 0
    # df['vibration_fft_std'] = 0

    # Lag features for last 5-10 minutes
    for lag in range(1, 11):
        for col in ['accel_x_mean', 'accel_y_mean', 'accel_z_mean', 'vibration_amp_mean', 'tilt_angle', 'rainfall', 'humidity', 'temperature']:
            if col in df.columns:
                df[f'{col}_lag_{lag}'] = df[col].shift(lag)

    # Drop rows with NaN values created by rolling and lag
    df.dropna(inplace=True)

    # Target columns remain the same
    return df.reset_index()

if __name__ == "__main__":
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    df = pd.read_csv(os.path.join(data_dir, 'sensor_data.csv'), parse_dates=['timestamp'])
    features_df = extract_features(df)
    features_df.to_csv(os.path.join(data_dir, 'processed_features.csv'), index=False)
    print("Features extracted and saved to data/processed_features.csv")
