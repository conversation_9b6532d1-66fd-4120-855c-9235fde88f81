# 🪨 Rockfall Prediction System

A machine learning system to predict rockfall events and estimate the time before they occur using real-time sensor data.

## Features

- **Real-time Prediction**: Uses accelerometer, vibration, tilt, rainfall, humidity, and temperature sensors
- **Dual Models**: SVM and Random Forest for classification, Random Forest for regression
- **Risk Assessment**: Classifies risk as Low, Moderate, or High
- **Time Estimation**: Estimates hours to rockfall for high-risk scenarios
- **Interactive UI**: Built with Streamlit for easy use
- **Simulation**: Test with predefined low, moderate, and high-risk scenarios
- **Alerts**: Visual alerts for high-risk situations

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Quick Start

Run the entire pipeline (data generation, preprocessing, training, and UI launch):
```bash
python run_pipeline.py
```

### Manual Steps

1. **Generate Dummy Data**:
   ```bash
   python src/data_generator.py
   ```

2. **Preprocess Data**:
   ```bash
   python src/preprocessor.py
   ```

3. **Train Models**:
   ```bash
   python src/train_models.py
   ```

4. **Launch UI**:
   ```bash
   streamlit run ui/app.py
   ```

## Data Description

### Input Sensors
- **Accelerometer**: X, Y, Z axes (m/s²)
- **Vibration**: Frequency (Hz) and Amplitude
- **Tilt**: Angle in degrees
- **Rainfall**: mm per hour
- **Humidity**: Percentage
- **Temperature**: Celsius

### Processed Features
- Statistical features: mean, std, max, min
- Rolling averages
- Frequency domain features (FFT)
- Lag features (last 5-10 minutes)

### Targets
- **rockfall_event**: Binary classification (0/1)
- **hours_to_rockfall**: Regression for time estimation

## Model Architecture

### Classification
- **SVM**: Support Vector Machine
- **Random Forest**: Ensemble method
- **Ensemble**: Majority vote for final prediction

### Regression
- **Random Forest**: Predicts hours to rockfall (when event=1)

### Risk Levels
- **Low**: No rockfall predicted
- **Moderate**: Rockfall predicted, but >24 hours away
- **High**: Rockfall predicted within 24 hours

## UI Features

- **Manual Input**: Adjust sensor values with sliders
- **Simulation**: Quick-load predefined scenarios
- **Real-time Prediction**: Instant risk assessment
- **Alerts**: Visual warnings for high-risk situations
- **Metrics Display**: Current sensor readings

## Project Structure

```
rockfall_prediction/
├── data/
│   ├── sensor_data.csv          # Raw dummy data
│   └── processed_features.csv   # ML-ready features
├── models/
│   ├── svm_classifier.pkl       # SVM model
│   ├── rf_classifier.pkl        # Random Forest classifier
│   └── rf_regressor.pkl         # Random Forest regressor
├── src/
│   ├── data_generator.py        # Generate dummy data
│   ├── preprocessor.py          # Feature extraction
│   ├── train_models.py          # Model training
│   └── predict.py               # Prediction functions
├── ui/
│   └── app.py                   # Streamlit UI
├── requirements.txt             # Python dependencies
├── run_pipeline.py              # Main pipeline runner
└── README.md                    # This file
```

## Technical Details

- **Language**: Python 3.8+
- **ML Framework**: scikit-learn
- **UI Framework**: Streamlit
- **Data Processing**: pandas, numpy
- **Visualization**: matplotlib
- **Serialization**: joblib

## Future Improvements

- Integrate with real sensor APIs
- Add more advanced models (LSTM, CNN)
- Implement real-time data streaming
- Add historical data visualization
- Deploy to cloud platform

## License

This project is open-source. Feel free to use and modify.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request.
