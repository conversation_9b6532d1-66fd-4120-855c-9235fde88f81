import streamlit as st
import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(__file__))
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from src.predict import predict_risk, simulate_scenario

st.set_page_config(page_title="Rockfall Prediction System", page_icon="🪨", layout="wide")

st.title("🪨 Rockfall Prediction System")
st.markdown("Predict rockfall events and estimate time to occurrence using real-time sensor data.")

# Sidebar for inputs
st.sidebar.header("Sensor Inputs")
accel_x = st.sidebar.slider("Accelerometer X (m/s²)", -2.0, 2.0, 0.0)
accel_y = st.sidebar.slider("Accelerometer Y (m/s²)", -2.0, 2.0, 0.0)
accel_z = st.sidebar.slider("Accelerometer Z (m/s²)", 9.0, 11.0, 9.8)
vibration_freq = st.sidebar.slider("Vibration Frequency (Hz)", 0, 100, 10)
vibration_amp = st.sidebar.slider("Vibration Amplitude", 0.0, 10.0, 1.0)
tilt_angle = st.sidebar.slider("Tilt Angle (degrees)", 0.0, 10.0, 1.0)
rainfall = st.sidebar.slider("Rainfall (mm/hour)", 0.0, 10.0, 0.1)
humidity = st.sidebar.slider("Humidity (%)", 0, 100, 50)
temperature = st.sidebar.slider("Temperature (°C)", 0, 40, 20)

input_data = {
    'accel_x': accel_x,
    'accel_y': accel_y,
    'accel_z': accel_z,
    'vibration_freq': vibration_freq,
    'vibration_amp': vibration_amp,
    'tilt_angle': tilt_angle,
    'rainfall': rainfall,
    'humidity': humidity,
    'temperature': temperature
}

# Simulation buttons
st.sidebar.header("Simulation Scenarios")
col1, col2, col3 = st.sidebar.columns(3)
with col1:
    if st.button("Low Risk"):
        input_data = simulate_scenario('low')
        st.sidebar.success("Low risk scenario loaded")
with col2:
    if st.button("Moderate Risk"):
        input_data = simulate_scenario('moderate')
        st.sidebar.success("Moderate risk scenario loaded")
with col3:
    if st.button("High Risk"):
        input_data = simulate_scenario('high')
        st.sidebar.success("High risk scenario loaded")

# Predict button
if st.button("Predict Rockfall Risk", type="primary"):
    with st.spinner("Analyzing sensor data..."):
        risk_level, hours_to_rockfall = predict_risk(input_data)

    # Display results
    if risk_level == 'Low':
        st.success(f"**Risk Level: {risk_level}**")
        st.info("No rockfall event predicted. No time estimation needed.")
    elif risk_level == 'Moderate':
        st.warning(f"**Risk Level: {risk_level}**")
        st.info("Rockfall event predicted, but time is not critical. No specific time estimation.")
    else:  # High
        st.error(f"**🚨 ALERT: {risk_level} Risk!**")
        st.error(f"Estimated hours to rockfall: {hours_to_rockfall:.1f}")
        if hours_to_rockfall <= 6:
            st.error("**URGENT: Immediate action required!**")

# Display current inputs
st.header("Current Sensor Readings")
col1, col2, col3 = st.columns(3)
with col1:
    st.metric("Accelerometer X", f"{input_data['accel_x']:.2f} m/s²")
    st.metric("Accelerometer Y", f"{input_data['accel_y']:.2f} m/s²")
    st.metric("Accelerometer Z", f"{input_data['accel_z']:.2f} m/s²")
with col2:
    st.metric("Vibration Freq", f"{input_data['vibration_freq']} Hz")
    st.metric("Vibration Amp", f"{input_data['vibration_amp']:.1f}")
    st.metric("Tilt Angle", f"{input_data['tilt_angle']:.1f}°")
with col3:
    st.metric("Rainfall", f"{input_data['rainfall']:.1f} mm/h")
    st.metric("Humidity", f"{input_data['humidity']}%")
    st.metric("Temperature", f"{input_data['temperature']}°C")

# Footer
st.markdown("---")
st.markdown("Built with ❤️ using Machine Learning for Rockfall Prediction")
