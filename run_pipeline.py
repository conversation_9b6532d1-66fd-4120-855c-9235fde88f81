#!/usr/bin/env python3
"""
Rockfall Prediction System - Main Pipeline Runner
"""

import os
import subprocess
import sys

def run_command(command, cwd=None):
    """Run a command and return success."""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ {command} completed successfully")
            return True
        else:
            print(f"✗ {command} failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Error running {command}: {e}")
        return False

def main():
    print("🪨 Rockfall Prediction System - Pipeline Runner")
    print("=" * 50)

    # Step 1: Generate dummy data (if not exists)
    data_file = os.path.join(os.path.dirname(__file__), 'data', 'sensor_data.csv')
    if os.path.exists(data_file):
        print("\n1. Sensor data already exists, skipping data generation...")
    else:
        print("\n1. Generating dummy sensor data...")
        if not run_command("python src/data_generator.py"):
            sys.exit(1)

    # Step 2: Preprocess data
    print("\n2. Preprocessing data and extracting features...")
    if not run_command("python src/preprocessor.py"):
        sys.exit(1)

    # Step 3: Train models
    print("\n3. Training machine learning models...")
    if not run_command("python src/train_models.py"):
        sys.exit(1)

    # Step 4: Launch UI
    print("\n4. Launching the prediction UI...")
    print("Open your browser to http://localhost:8501 to use the application")
    run_command("streamlit run ui/app.py")

if __name__ == "__main__":
    main()
